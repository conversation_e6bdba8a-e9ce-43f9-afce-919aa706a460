package com.fh.cp.service.controller;

import com.fh.cp.service.cp.api.CpSchoolPublicCourseOpenApi;
import com.light.core.entity.AjaxResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * @Author: liuzeyu
 * @CreateTime: 2024-09-27  09:32
 */
@Api("公共资源开通接口")
@RestController
@RequestMapping("/cp/public-course/open")
public class CpSchoolPublicCourseOpenController {
    @Resource
    private CpSchoolPublicCourseOpenApi cpSchoolPublicCourseOpenApi;

    /**
     * 校验公共资源是否开通
     *
     * @param organizationId
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2024/9/27 9:41
     **/
    @GetMapping("/check")
    @ApiOperation(value = "校验是否开通公共资源")
    public AjaxResult checkPublicCourseOpen(@RequestParam Long organizationId) {
        return cpSchoolPublicCourseOpenApi.checkPublicCourseOpen(organizationId);
    }
}
