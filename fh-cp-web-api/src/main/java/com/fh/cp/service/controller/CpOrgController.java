package com.fh.cp.service.controller;

import com.alibaba.fastjson.JSONObject;
import com.fh.cp.service.cp.api.BaseDataApi;
import com.fh.cp.service.cp.api.CpInstitutionApplyApi;
import com.fh.cp.service.cp.api.CpInstitutionRegionApi;
import com.fh.cp.service.cp.entity.bo.CpInstitutionApplyBo;
import com.fh.cp.service.cp.entity.vo.CpInstitutionApplyVo;
import com.fh.cp.service.cp.entity.vo.OrganizationVoExt;
import com.fh.cp.service.enums.OrgType;
import com.light.core.entity.AjaxResult;
import com.light.user.organization.entity.bo.OrganizationBo;
import com.light.user.organization.entity.vo.OrganizationSetupVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

/**
 * 组织信息
 *
 * <AUTHOR>
 * @date 2023-07-10 17:49
 */
@RestController
@RequestMapping("/cp/org")
@Slf4j
@Api(value = "组织信息", tags = "组织信息")
public class CpOrgController {
    @Resource
    BaseDataApi baseDataApi;
    @Resource
    CpInstitutionRegionApi cpInstitutionRegionApi;
    @Resource
    CpInstitutionApplyApi cpInstitutionApplyApi;

    /**
     * 获取组织信息
     *
     * @param organizationId
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2023/7/10 17:52
     **/
    @GetMapping("/org-info")
    @ApiOperation(value = "获取组织信息", notes = "获取组织信息")
    public AjaxResult orgInfo(@RequestParam("organizationId") Long organizationId) {
        Map<String, Object> map = new HashMap<>();
        AjaxResult orgResult = baseDataApi.getOrganizationVoByOrgId(organizationId);
        if (orgResult.isFail() || orgResult.getData() == null) {
            return orgResult;
        }
        OrganizationVoExt organizationVo = JSONObject.parseObject(JSONObject.
                toJSONString(orgResult.getData()), OrganizationVoExt.class);
        // 副标题，logo,建校日期存org_setup
        AjaxResult<OrganizationSetupVo> byOrgId = baseDataApi.getByOrgId(organizationId);
        if (byOrgId.isSuccess() && byOrgId.getData() != null) {
            OrganizationSetupVo organizationSetupVo = byOrgId.getData();
            organizationVo.setLogo(organizationSetupVo.getLogo());
            organizationVo.setWebName(organizationSetupVo.getWebName());
            organizationVo.setOtherConfig(organizationSetupVo.getOtherConfig());
        }

        if (OrgType.SCHOOL.getCode().equals(organizationVo.getType())) {
            AjaxResult parentOrgResult = baseDataApi
                    .getOrganizationVoByOrgId(organizationVo.getParentId());
            if (parentOrgResult.isSuccess() && parentOrgResult.getData() != null) {
                OrganizationVoExt parentOrg = JSONObject.parseObject(JSONObject.
                        toJSONString(parentOrgResult.getData()), OrganizationVoExt.class);
                organizationVo.setParentName(parentOrg.getName());
            }
        }

        if (OrgType.THIRD_INSTITUTION.getCode().equals(organizationVo.getType())) {
            CpInstitutionApplyVo cpInstitutionApplyVo = cpInstitutionApplyApi
                    .getDetailByInstitutionId(organizationVo.getId()).getData();
            organizationVo.setCoverId(cpInstitutionApplyVo.getInstitutionIconId());
            organizationVo.setBusinessLicenseId(cpInstitutionApplyVo.getInstitutionLicenseId());

            organizationVo.setRegions(cpInstitutionRegionApi
                    .getRegionsByInstitutionId(organizationVo.getId())
                    .getData());
        }

        map.put("organizationVo", organizationVo);
        return AjaxResult.success(map);
    }

    /**
     * 编辑机构信息
     *
     * @param cpInstitutionApplyBo
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2023/7/12 10:22
     **/
    @PostMapping("/update")
    @ApiOperation(value = "编辑机构信息", notes = "编辑机构信息")
    public AjaxResult update(@RequestBody CpInstitutionApplyBo cpInstitutionApplyBo) {
        OrganizationBo organizationBo = new OrganizationBo();
        organizationBo.setId(cpInstitutionApplyBo.getInstitutionId());
        organizationBo.setParentId(cpInstitutionApplyBo.getParentId());
        organizationBo.setAddress(cpInstitutionApplyBo.getInstitutionAddress());
        organizationBo.setName(cpInstitutionApplyBo.getInstitutionName());
        organizationBo.setConcatName(cpInstitutionApplyBo.getInstitutionConnectUser());
        organizationBo.setBusinessLicenseUrl(cpInstitutionApplyBo.getInstitutionLicenseUrl());
        organizationBo.setSocialCreditCode(cpInstitutionApplyBo.getInstitutionLicenseCode());
        organizationBo.setLegalPersonName(cpInstitutionApplyBo.getInstitutionMaster());
        organizationBo.setCover(cpInstitutionApplyBo.getInstitutionIconUrl());
        organizationBo.setContact(cpInstitutionApplyBo.getInstitutionConnectMobile());

        AjaxResult<Boolean> updateOrgResult = baseDataApi.updateOrganization(organizationBo);
        if (updateOrgResult.isSuccess() && updateOrgResult.getData()) {
            CpInstitutionApplyVo cpInstitutionApplyVo = cpInstitutionApplyApi
                    .getDetailByInstitutionId(cpInstitutionApplyBo.getInstitutionId()).getData();
            cpInstitutionApplyBo.setInstitutionApplyId(cpInstitutionApplyVo.getInstitutionApplyId());
            cpInstitutionApplyApi.updateCpInstitutionApply(cpInstitutionApplyBo);
            return AjaxResult.success("编辑机构信息成功");
        }
        return AjaxResult.fail("编辑机构信息失败");
    }
}
