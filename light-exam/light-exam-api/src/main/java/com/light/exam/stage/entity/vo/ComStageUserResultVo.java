package com.light.exam.stage.entity.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
/**
 * 竞赛环节用户结果
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-03-28 16:07:51
 */
@Data
public class ComStageUserResultVo implements Serializable{

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    private Long id;

    /**
     * 竞赛ID
     */
    private Long comId;

    /**
     * 环节ID
     */
    private Long stageId;

    /**
     * 用户oid
     */
    private String userOid;

    /**
     * 分数
     */
    private Double score;

    /**
     * 正确率
     */
    private Double rightsPercent;

    /**
     * 答题用时（毫秒）
     */
    private Long useTime;

    /**
     * 答题数
     */
    private Long itemCount;

    /**
     * 描述
     */
    private String description;

    /**
     * 顺序
     */
    private Integer sequence;

    /**
     * 更新人
     */
    private String updateBy;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    private String stageSetting;

}
