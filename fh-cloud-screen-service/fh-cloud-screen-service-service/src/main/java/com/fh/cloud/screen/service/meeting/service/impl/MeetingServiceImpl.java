package com.fh.cloud.screen.service.meeting.service.impl;

import cn.hutool.core.lang.UUID;
import cn.hutool.core.util.NumberUtil;
import com.alibaba.nacos.common.utils.CollectionUtils;
import com.alibaba.nacos.common.utils.UuidUtils;
import com.alibaba.nacos.shaded.com.google.common.collect.Lists;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.fh.cloud.screen.service.baseinfo.BaseDataService;
import com.fh.cloud.screen.service.consts.ConstantsInteger;
import com.fh.cloud.screen.service.consts.ConstantsRedis;
import com.fh.cloud.screen.service.device.entity.vo.ShowDeviceVo;
import com.fh.cloud.screen.service.device.service.IShowDeviceService;
import com.fh.cloud.screen.service.enums.MeetingEnums;
import com.fh.cloud.screen.service.enums.MessageWsType;
import com.fh.cloud.screen.service.enums.SpaceGroupUseType;
import com.fh.cloud.screen.service.event.PublishEvent;
import com.fh.cloud.screen.service.meeting.entity.bo.MeetingLongConditionBo;
import com.fh.cloud.screen.service.meeting.entity.bo.MeetingUserBo;
import com.fh.cloud.screen.service.meeting.entity.bo.MeetingUserConditionBo;
import com.fh.cloud.screen.service.meeting.entity.dto.MeetingLongDto;
import com.fh.cloud.screen.service.meeting.entity.dto.MeetingUserDto;
import com.fh.cloud.screen.service.meeting.entity.vo.MeetingGroupBySpaceVo;
import com.fh.cloud.screen.service.meeting.entity.vo.MeetingLongVo;
import com.fh.cloud.screen.service.meeting.entity.vo.MeetingUserVo;
import com.fh.cloud.screen.service.meeting.service.IMeetingLongService;
import com.fh.cloud.screen.service.meeting.service.IMeetingService;
import com.fh.cloud.screen.service.meeting.service.IMeetingUserService;
import com.fh.cloud.screen.service.space.entity.bo.SpaceInfoListConditionBo;
import com.fh.cloud.screen.service.space.entity.vo.SpaceInfoVo;
import com.fh.cloud.screen.service.space.service.ISpaceInfoService;
import com.fh.cloud.screen.service.utils.DateKit;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.light.core.utils.FuzzyQueryUtil;
import com.light.redis.component.RedisComponent;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

import com.light.core.enums.StatusEnum;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import javax.annotation.Resource;

import com.fh.cloud.screen.service.meeting.entity.dto.MeetingDto;
import com.fh.cloud.screen.service.meeting.entity.bo.MeetingConditionBo;
import com.fh.cloud.screen.service.meeting.entity.bo.MeetingBo;
import com.fh.cloud.screen.service.meeting.entity.vo.MeetingVo;
import com.fh.cloud.screen.service.meeting.mapper.MeetingMapper;
import com.light.core.entity.AjaxResult;
import org.springframework.transaction.annotation.Transactional;

/**
 * 会议表接口实现类
 *
 * <AUTHOR>
 * @date 2022-08-16 17:51:00
 */
@Service
public class MeetingServiceImpl extends ServiceImpl<MeetingMapper, MeetingDto> implements IMeetingService {

    @Resource
    private MeetingMapper meetingMapper;
    @Resource
    private IMeetingUserService meetingUserService;
    @Autowired
    private BaseDataService baseDataService;
    @Resource
    private ISpaceInfoService spaceInfoService;
    @Autowired
    private ApplicationContext applicationContext;
    @Autowired
    private IShowDeviceService showDeviceService;
    @Resource
    private RedisComponent redisComponent;
    @Resource
    private IMeetingLongService meetingLongService;

    @Override
    public List<MeetingVo> getMeetingListByCondition(MeetingConditionBo condition) {
        condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
        FuzzyQueryUtil.transferMeanBean(condition);

        List<MeetingVo> meetingRelationList = meetingMapper.getMeetingRelationList(condition);
        List<MeetingDto> updateList = new ArrayList<>();
        // 封装用户姓名
        if (CollectionUtils.isNotEmpty(meetingRelationList)) {
            List<String> userOids =
                meetingRelationList.stream().map(MeetingVo::getUserOid).distinct().collect(Collectors.toList());
            Map<String, String> userNameMap = baseDataService.getRealNameByUserOids(userOids);
            Date now = new Date();
            for (MeetingVo meetingVo : meetingRelationList) {
                meetingVo.setUserName(userNameMap.get(meetingVo.getUserOid()));
                // 根据时间判断状态并更新数据库
                Date startDay =
                    DateKit.getDateAndTimeCompose(meetingVo.getMeetingDate(), meetingVo.getMeetingStartTime());
                Date endDay = DateKit.getDateAndTimeCompose(meetingVo.getMeetingDate(), meetingVo.getMeetingEndTime());
                if (now.after(startDay)
                    && !meetingVo.getStatus().equals(MeetingEnums.MEETING_STATUS_EARLY_END.getCode())) {
                    MeetingDto meetingDto = new MeetingDto();
                    meetingDto.setMeetingId(meetingVo.getMeetingId());
                    meetingDto.setStatus(MeetingEnums.MEETING_STATUS_END.getCode());
                    meetingVo.setStatus(MeetingEnums.MEETING_STATUS_END.getCode());
                    if (endDay.after(now)) {
                        meetingDto.setStatus(MeetingEnums.MEETING_STATUS_START.getCode());
                        meetingVo.setStatus(MeetingEnums.MEETING_STATUS_START.getCode());
                    }
                    updateList.add(meetingDto);
                }
            }
            if (CollectionUtils.isNotEmpty(updateList)) {
                this.updateBatchById(updateList);
            }
        }
        return meetingRelationList;
    }

    @Override
    public Map<String, Object> getMyMeetingListByCondition(MeetingConditionBo condition) {
        Map<String, Object> map = new HashMap<>();
        map.put("list", null);
        map.put("total", null);
        MeetingUserConditionBo meetingUserConditionBo = new MeetingUserConditionBo();
        meetingUserConditionBo.setUserOid(condition.getUserOid());
        meetingUserConditionBo.setIsDelete(StatusEnum.NOTDELETE.getCode());
        PageInfo<MeetingUserVo> pageInfo =
            new PageInfo<>(meetingUserService.getMeetingUserListByCondition(meetingUserConditionBo));
        if (CollectionUtils.isNotEmpty(pageInfo.getList())) {
            List<Long> meetingIds =
                pageInfo.getList().stream().map(MeetingUserVo::getMeetingId).collect(Collectors.toList());
            condition.setMeetingIds(meetingIds);
            condition.setUserOid(null);
            PageHelper.startPage(condition.getPageNo(), condition.getPageSize(), condition.getOrderBy());
            PageInfo<MeetingVo> meetingVoPageInfo = new PageInfo<>(getMeetingListByCondition(condition));
            map.put("list", meetingVoPageInfo.getList());
            map.put("total", meetingVoPageInfo.getTotal());
        }
        return map;
    }

    @Override
    public List<MeetingGroupBySpaceVo> getMeetingListByDate(MeetingConditionBo condition) {
        // 前端判断会议标识，不用于检索
        final String userOid = condition.getUserOid();
        condition.setUserOid(null);
        // 获取所有会议室-非行政地点
        SpaceInfoListConditionBo spaceInfoListConditionBo = new SpaceInfoListConditionBo();
        spaceInfoListConditionBo.setMeetingUse(StatusEnum.YES.getCode());
        spaceInfoListConditionBo.setOrganizationId(condition.getOrganizationId());
        List<SpaceInfoVo> spaceInfoVoList = spaceInfoService.getSpaceInfoListByCondition(spaceInfoListConditionBo);
        if (CollectionUtils.isEmpty(spaceInfoVoList)) {
            return null;
        }
        List<SpaceInfoVo> sortedList = spaceInfoVoList.stream()
            .sorted(Comparator.comparing(SpaceInfoVo::getSpaceGroupId)).collect(Collectors.toList());
        List<MeetingGroupBySpaceVo> convertList = sortedList.stream().map(space -> {
            MeetingGroupBySpaceVo meetingGroupBySpaceVo = new MeetingGroupBySpaceVo();
            meetingGroupBySpaceVo.setMeetingName(space.getSpaceInfoName());
            meetingGroupBySpaceVo.setSpaceInfoId(space.getSpaceInfoId());
            StringBuilder stringBuilder = new StringBuilder("");
            if (null != space.getUserCapacity()) {
                stringBuilder.append(space.getUserCapacity().toString().concat("人"));
            }
            if (null != space.getComputerUse() && StatusEnum.YES.getCode().equals(space.getComputerUse())) {
                stringBuilder.append(" 电脑");
            }
            if (null != space.getNetworkUse() && StatusEnum.YES.getCode().equals(space.getNetworkUse())) {
                stringBuilder.append(" 网络");
            }
            if (null != space.getShadowUse() && StatusEnum.YES.getCode().equals(space.getShadowUse())) {
                stringBuilder.append(" 投影");
            }
            meetingGroupBySpaceVo.setSpaceProperty(stringBuilder.toString());
            return meetingGroupBySpaceVo;
        }).collect(Collectors.toList());

        // 不分页获取会议，根据地点分组
        condition.setOrderByMeetingNameFlag(StatusEnum.YES.getCode());
        List<MeetingVo> meetingVos = getMeetingListByCondition(condition);
        if (CollectionUtils.isNotEmpty(meetingVos)) {
            meetingVos.forEach(meetingVo -> {
                if (meetingVo.getUserOid().equals(userOid)) {
                    meetingVo.setCurrentUserFlag(true);
                }
            });
            Map<Long, List<MeetingVo>> meetingGroupMap =
                meetingVos.stream().collect(Collectors.groupingBy(MeetingVo::getSpaceInfoId));
            Set<Long> keys = meetingGroupMap.keySet();

            // 地点组装并匹配会议
            for (MeetingGroupBySpaceVo groupBySpaceVo : convertList) {
                for (Long key : keys) {
                    if (groupBySpaceVo.getSpaceInfoId().equals(key)) {
                        groupBySpaceVo.setMeetingVos(meetingGroupMap.get(key));
                    }
                }
            }
        }
        return convertList;
    }

    @Override
    @Transactional
    public AjaxResult addMeeting(MeetingBo meetingBo) {
        MeetingDto meeting = new MeetingDto();
        BeanUtils.copyProperties(meetingBo, meeting);
        Date startDay = DateKit.getDateAndTimeCompose(meetingBo.getMeetingDate(), meetingBo.getMeetingStartTime());
        Date now = new Date();
        if (now.after(startDay)) {
            return AjaxResult.fail("预约时间必须在当前时间之后");
        }
        // 预约时间冲突校验
        MeetingVo meetingVo = checkMeetingDateRepeat(meeting);
        if (null != meetingVo) {
            return AjaxResult
                .fail("与" + meetingVo.getTitle() + "会议时间" + DateKit.getStringTime(meetingVo.getMeetingStartTime()) + "-"
                    + DateKit.getStringTime(meetingVo.getMeetingEndTime()) + "冲突");
        }
        // 长期预约时间冲突校验（不是定时器且不是当天的需要校验）
        if (!meetingBo.isTriggerExecute() && !DateKit.checkOneDay(meetingBo.getMeetingDate(), new Date())) {
            MeetingLongVo meetingLongVo = checkMeetingLongDateRepeat(meeting);
            if (null != meetingLongVo) {
                return AjaxResult.fail("与长期预约" + meetingLongVo.getTitle() + "预约时间"
                    + DateKit.getStringTime(meetingLongVo.getMeetingStartTime()) + "-"
                    + DateKit.getStringTime(meetingLongVo.getMeetingEndTime()) + "冲突");
            }
        }

        // 记录db会议已存在人员
        Map<String, MeetingUserDto> dbMeetingUserMap = new HashMap<>();
        if (null != meeting.getMeetingId()) {
            LambdaQueryWrapper<MeetingUserDto> lqw =
                new LambdaQueryWrapper<MeetingUserDto>().eq(MeetingUserDto::getMeetingId, meeting.getMeetingId())
                    .eq(MeetingUserDto::getIsDelete, StatusEnum.NOTDELETE.getCode());
            List<MeetingUserDto> dbMeetingUsers = meetingUserService.list(lqw);
            if (CollectionUtils.isNotEmpty(dbMeetingUsers)) {
                dbMeetingUserMap = dbMeetingUsers.stream()
                    .collect(Collectors.toMap(MeetingUserDto::getUserOid, x -> x, (k1, k2) -> k2));
                meetingUserService.remove(lqw);
            }
        }
        // 新增或修改会议
        meeting.setStatus(MeetingEnums.MEETING_STATUS_NOT.getCode());
        if (saveOrUpdate(meeting)) {
            List<MeetingUserBo> meetingUserBos = meetingBo.getMeetingUserBos();
            Map<String, MeetingUserDto> finalDbMeetingUserMap = dbMeetingUserMap;
            List<MeetingUserDto> meetingUserDtoList = meetingUserBos.stream().map(meetingUserBo -> {
                MeetingUserDto meetingUserDto = new MeetingUserDto();
                BeanUtils.copyProperties(meetingUserBo, meetingUserDto);
                MeetingUserDto existMeetingUser = finalDbMeetingUserMap.get(meetingUserDto.getUserOid());
                meetingUserDto.setStatus(MeetingEnums.SIGN_NOT.getCode());
                meetingUserDto.setMeetingId(meeting.getMeetingId());
                if (null != existMeetingUser) {
                    meetingUserDto.setStatus(existMeetingUser.getStatus());
                    meetingUserDto.setSignTime(existMeetingUser.getSignTime());
                }
                return meetingUserDto;
            }).collect(Collectors.toList());
            meetingUserService.saveBatch(meetingUserDtoList);
            // 会议修改事件推送云屏app
            publishEventByMeetingModify(meeting.getOrganizationId(), meeting.getSpaceInfoId(),
                meeting.getSpaceGroupUseType());
            // 修改删除redis 会议缓存
            if (null != meetingBo.getMeetingId()) {
                redisComponent.del(ConstantsRedis.MEETING_REDIS_KEY.concat(meeting.getMeetingId().toString()));
            }
            return AjaxResult.success("保存成功");
        } else {
            return AjaxResult.fail("保存失败");
        }
    }

    /**
     * 根据日期批量新增-----已废弃
     * 
     * @param meetingBo
     * @return
     */
    @Deprecated
    @Override
    @Transactional
    public AjaxResult addMeetingBatch(MeetingBo meetingBo) {
        String meetingUuid = UUID.fastUUID().toString();
        for (Date meetingDate : meetingBo.getMeetingDateList()) {
            meetingBo.setMeetingUuid(meetingUuid);
            MeetingDto meeting = new MeetingDto();
            BeanUtils.copyProperties(meetingBo, meeting);
            meeting.setMeetingDate(meetingDate);
            Date startDay = DateKit.getDateAndTimeCompose(meetingDate, meetingBo.getMeetingStartTime());
            Date now = new Date();
            if (now.after(startDay)) {
                return AjaxResult.fail("预约时间必须在当前时间之后");
            }
            // 预约时间冲突校验
            MeetingVo meetingVo = checkMeetingDateRepeat(meeting);
            if (null != meetingVo) {
                return AjaxResult
                    .fail("与" + meetingVo.getTitle() + "预约时间" + DateKit.getStringTime(meetingVo.getMeetingStartTime())
                        + "-" + DateKit.getStringTime(meetingVo.getMeetingEndTime()) + "冲突");
            }
            MeetingLongVo meetingLongVo = checkMeetingLongDateRepeat(meeting);
            if (null != meetingLongVo) {
                return AjaxResult.fail("与长期预约" + meetingLongVo.getTitle() + "预约时间"
                    + DateKit.getStringTime(meetingLongVo.getMeetingStartTime()) + "-"
                    + DateKit.getStringTime(meetingLongVo.getMeetingEndTime()) + "冲突");
            }

            // 记录db会议已存在人员
            Map<String, MeetingUserDto> dbMeetingUserMap = new HashMap<>();
            if (null != meeting.getMeetingId()) {
                LambdaQueryWrapper<MeetingUserDto> lqw =
                    new LambdaQueryWrapper<MeetingUserDto>().eq(MeetingUserDto::getMeetingId, meeting.getMeetingId())
                        .eq(MeetingUserDto::getIsDelete, StatusEnum.NOTDELETE.getCode());
                List<MeetingUserDto> dbMeetingUsers = meetingUserService.list(lqw);
                if (CollectionUtils.isNotEmpty(dbMeetingUsers)) {
                    dbMeetingUserMap = dbMeetingUsers.stream()
                        .collect(Collectors.toMap(MeetingUserDto::getUserOid, x -> x, (k1, k2) -> k2));
                    meetingUserService.remove(lqw);
                }
            }
            // 新增或修改会议
            meeting.setStatus(MeetingEnums.MEETING_STATUS_NOT.getCode());
            if (saveOrUpdate(meeting)) {
                List<MeetingUserBo> meetingUserBos = meetingBo.getMeetingUserBos();
                Map<String, MeetingUserDto> finalDbMeetingUserMap = dbMeetingUserMap;
                List<MeetingUserDto> meetingUserDtoList = meetingUserBos.stream().map(meetingUserBo -> {
                    MeetingUserDto meetingUserDto = new MeetingUserDto();
                    BeanUtils.copyProperties(meetingUserBo, meetingUserDto);
                    MeetingUserDto existMeetingUser = finalDbMeetingUserMap.get(meetingUserDto.getUserOid());
                    meetingUserDto.setStatus(MeetingEnums.SIGN_NOT.getCode());
                    meetingUserDto.setMeetingId(meeting.getMeetingId());
                    if (null != existMeetingUser) {
                        meetingUserDto.setStatus(existMeetingUser.getStatus());
                        meetingUserDto.setSignTime(existMeetingUser.getSignTime());
                    }
                    return meetingUserDto;
                }).collect(Collectors.toList());
                meetingUserService.saveBatch(meetingUserDtoList);
                // 修改删除redis 会议缓存
                if (null != meetingBo.getMeetingId()) {
                    redisComponent.del(ConstantsRedis.MEETING_REDIS_KEY.concat(meeting.getMeetingId().toString()));
                }
            } else {
                return AjaxResult.fail("保存失败");
            }
        }
        // 会议修改事件推送云屏app，注意目前仅仅能预定非行政教室，所以这里只需要传递id即可
        publishEventByMeetingModify(meetingBo.getOrganizationId(), meetingBo.getSpaceInfoId(),
            meetingBo.getSpaceGroupUseType());
        return AjaxResult.success("保存成功");
    }

    /**
     * 会议时间冲突校验
     *
     * @param meeting
     * @return com.fh.cloud.screen.service.meeting.entity.vo.MeetingVo
     * <AUTHOR>
     * @date 2022/10/31 16:53
     */
    private MeetingVo checkMeetingDateRepeat(MeetingDto meeting) {
        MeetingConditionBo meetingConditionBo = new MeetingConditionBo();
        meetingConditionBo.setOrganizationId(meeting.getOrganizationId());
        meetingConditionBo.setMeetingDate(meeting.getMeetingDate());
        meetingConditionBo.setSpaceGroupUseType(meeting.getSpaceGroupUseType());
        meetingConditionBo.setSpaceInfoId(meeting.getSpaceInfoId());
        meetingConditionBo.setNotEnd(StatusEnum.YES.getCode());
        List<MeetingVo> meetingVos = this.getMeetingListByCondition(meetingConditionBo);

        if (CollectionUtils.isNotEmpty(meetingVos)) {
            // 只校验时间，不校验日期
            Date date = new Date();
            Date meetingStartDateTime = DateKit.getDateAndTimeCompose(date, meeting.getMeetingStartTime());
            Date meetingEndDateTime = DateKit.getDateAndTimeCompose(date, meeting.getMeetingEndTime());
            for (MeetingVo vo : meetingVos) {
                if (vo.getMeetingId().equals(meeting.getMeetingId())) {
                    continue;
                }
                Date startDateTime = DateKit.getDateAndTimeCompose(date, vo.getMeetingStartTime());
                Date endDateTime = DateKit.getDateAndTimeCompose(date, vo.getMeetingEndTime());
                // 开始时间相等
                if (meetingStartDateTime.equals(startDateTime)) {
                    return vo;
                }
                // 当前开始时间在 已有开始、结束时间范围内
                if (meetingStartDateTime.after(startDateTime) && meetingStartDateTime.before(endDateTime)) {
                    return vo;
                }
                // 当前结束时间，在已有开始、结束时间范围内。
                if (meetingEndDateTime.after(startDateTime) && meetingEndDateTime.before(endDateTime)) {
                    return vo;
                }
                // 当前时间段，包含已有时间段
                if (meetingStartDateTime.before(startDateTime)
                    && (meetingEndDateTime.after(endDateTime) || meetingEndDateTime.equals(endDateTime))) {
                    return vo;
                }
            }
        }
        return null;
    }

    /**
     * 长期会议时间冲突校验
     *
     * @param meeting the meeting
     * @return the meeting vo
     * <AUTHOR>
     * @date 2023 -12-12 09:44:41
     */
    private MeetingLongVo checkMeetingLongDateRepeat(MeetingDto meeting) {
        MeetingLongConditionBo meetingLongConditionBo = new MeetingLongConditionBo();
        meetingLongConditionBo.setOrganizationId(meeting.getOrganizationId());
        meetingLongConditionBo.setMeetingStartDate(meeting.getMeetingDate());
        meetingLongConditionBo.setMeetingEndDate(meeting.getMeetingDate());
        meetingLongConditionBo.setSpaceGroupUseType(meeting.getSpaceGroupUseType());
        meetingLongConditionBo.setSpaceInfoId(meeting.getSpaceInfoId());
        meetingLongConditionBo.setQueryRepeat(true);
        List<MeetingLongVo> meetingLongVos = meetingLongService.getMeetingLongListByCondition(meetingLongConditionBo);

        if (CollectionUtils.isNotEmpty(meetingLongVos)) {
            // 只校验时间，不校验日期
            Date date = new Date();
            Date meetingStartDateTime = DateKit.getDateAndTimeCompose(date, meeting.getMeetingStartTime());
            Date meetingEndDateTime = DateKit.getDateAndTimeCompose(date, meeting.getMeetingEndTime());
            for (MeetingLongVo vo : meetingLongVos) {
                Date startDateTime = DateKit.getDateAndTimeCompose(date, vo.getMeetingStartTime());
                Date endDateTime = DateKit.getDateAndTimeCompose(date, vo.getMeetingEndTime());
                // 开始时间相等
                if (meetingStartDateTime.equals(startDateTime)) {
                    return vo;
                }
                // 当前开始时间在 已有开始、结束时间范围内
                if (meetingStartDateTime.after(startDateTime) && meetingStartDateTime.before(endDateTime)) {
                    return vo;
                }
                // 当前结束时间，在已有开始、结束时间范围内。
                if (meetingEndDateTime.after(startDateTime) && meetingEndDateTime.before(endDateTime)) {
                    return vo;
                }
                // 当前时间段，包含已有时间段
                if (meetingStartDateTime.before(startDateTime)
                    && (meetingEndDateTime.after(endDateTime) || meetingEndDateTime.equals(endDateTime))) {
                    return vo;
                }
            }
        }
        return null;
    }

    @Override
    public AjaxResult updateMeeting(MeetingBo meetingBo) {
        MeetingDto meeting = new MeetingDto();
        BeanUtils.copyProperties(meetingBo, meeting);
        MeetingDto meetingDto = meetingMapper.selectById(meeting.getMeetingId());
        if (null == meetingDto || StatusEnum.ISDELETE.getCode().equals(meetingDto.getIsDelete())) {
            return AjaxResult.fail("会议不存在，修改失败");
        }
        if (updateById(meeting)) {
            // 会议修改事件推送云屏app
            publishEventByMeetingModify(meetingDto.getOrganizationId(), meetingDto.getSpaceInfoId(),
                meetingDto.getSpaceGroupUseType());
            // 提前结束会议-删除缓存
            redisComponent.del(ConstantsRedis.MEETING_REDIS_KEY.concat(meetingBo.getMeetingId().toString()));
            return AjaxResult.success("保存成功");
        } else {
            return AjaxResult.fail("保存失败");
        }
    }

    @Override
    public MeetingVo getDetail(Long meetingId) {
        MeetingConditionBo condition = new MeetingConditionBo();
        condition.setMeetingId(meetingId);
        condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
        List<MeetingVo> meetingVos = getMeetingListByCondition(condition);
        MeetingVo vo = new MeetingVo();
        if (CollectionUtils.isNotEmpty(meetingVos)) {
            vo = meetingVos.get(0);
            LambdaQueryWrapper<MeetingUserDto> lambdaQueryWrapper = new LambdaQueryWrapper<>();
            lambdaQueryWrapper.eq(MeetingUserDto::getMeetingId, meetingId);
            lambdaQueryWrapper.eq(MeetingUserDto::getIsDelete, StatusEnum.NOTDELETE.getCode());
            List<MeetingUserDto> meetingUserDtos = meetingUserService.list(lambdaQueryWrapper);
            if (CollectionUtils.isNotEmpty(meetingUserDtos)) {
                AtomicReference<Integer> signCount = new AtomicReference<>(0);
                boolean isSign = false;
                if (StatusEnum.YES.getCode().equals(vo.getIsSignIn())) {
                    isSign = true;
                }
                List<String> userOids =
                    meetingUserDtos.stream().map(MeetingUserDto::getUserOid).collect(Collectors.toList());
                Map<String, String> userNameMap = baseDataService.getRealNameByUserOids(userOids);
                boolean finalIsSign = isSign;
                List<MeetingUserVo> meetingUserVos = meetingUserDtos.stream().map(meetingUserDto -> {
                    MeetingUserVo meetingUserVo = new MeetingUserVo();
                    BeanUtils.copyProperties(meetingUserDto, meetingUserVo);
                    meetingUserVo.setUserName(userNameMap.get(meetingUserVo.getUserOid()));
                    if (finalIsSign && null != meetingUserVo.getStatus()
                        && !StatusEnum.YES.getCode().equals(meetingUserVo.getStatus())) {
                        signCount.set(signCount.get() + 1);
                    }
                    return meetingUserVo;
                }).collect(Collectors.toList());
                vo.setMeetingUserVos(meetingUserVos);
                // 设置签到比例
                if (0 == signCount.get()) {
                    vo.setPercent(new BigDecimal(0.0));
                } else {
                    vo.setPercent(NumberUtil.mul(NumberUtil.div(signCount.get(), vo.getUserCount()), 100));
                }
            }
        }
        return vo;
    }

    @Override
    @Transactional
    public AjaxResult delete(MeetingBo meetingBo) {
        MeetingDto meetingDto = getById(meetingBo.getMeetingId());
        if (null == meetingDto) {
            return AjaxResult.fail("会议不存在");
        }
        if (StatusEnum.ISDELETE.getCode().equals(meetingDto.getIsDelete())) {
            return AjaxResult.success();
        }
        // 会议已结束、提前结束、会议室申请人可以删除会议 ----> 20231211改为只要看到删除按钮即可删除
        LambdaUpdateWrapper<MeetingUserDto> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(MeetingUserDto::getMeetingId, meetingDto.getMeetingId());
        updateWrapper.set(MeetingUserDto::getIsDelete, StatusEnum.ISDELETE.getCode());
        meetingUserService.update(updateWrapper);
        MeetingDto meeting = new MeetingDto();
        meeting.setIsDelete(StatusEnum.ISDELETE.getCode());
        meeting.setMeetingId(meetingDto.getMeetingId());
        this.updateById(meeting);
        // 会议修改事件推送云屏app
        publishEventByMeetingModify(meetingDto.getOrganizationId(), meetingDto.getSpaceInfoId(),
            meetingDto.getSpaceGroupUseType());
        // 同步删除缓存
        redisComponent.del(ConstantsRedis.MEETING_REDIS_KEY.concat(meetingBo.getMeetingId().toString()));
        return AjaxResult.success();
    }

    @Override
    public AjaxResult getNowAndNextMeeting(MeetingConditionBo conditionBo) {
        Date screenNowDate = conditionBo.getScreenNowDate();
        // update by sunqb at 20231212更新：和产品沟通不展示第二天的会议，因此注释掉
        // conditionBo.setStartDate(DateKit.getDayZore(screenNowDate));
        conditionBo.setMeetingDate(DateKit.getDayZore(screenNowDate));
        conditionBo.setOrderBy("meeting_date ASC, meeting_start_time ASC");
        conditionBo.setIsDelete(StatusEnum.NOTDELETE.getCode());
        conditionBo.setNotEnd(StatusEnum.YES.getCode());
        List<MeetingVo> meetingVos = meetingMapper.getMeetingListByCondition(conditionBo);
        List<MeetingVo> nowMeetingVos = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(meetingVos)) {
            // 过滤出当前地点正在开始的会议及之后的会议,提前30分钟拉取，开始时间往前推30分钟
            for (int i = 0; i < meetingVos.size(); i++) {
                Date start = DateKit.getDateAndTimeCompose(meetingVos.get(i).getMeetingDate(),
                    meetingVos.get(i).getMeetingStartTime());
                start = DateKit.addMinute(start, ConstantsInteger.MEETING_SCENE_BEFORE_TIME);
                Date end = DateKit.getDateAndTimeCompose(meetingVos.get(i).getMeetingDate(),
                    meetingVos.get(i).getMeetingEndTime());
                if (screenNowDate.before(end) && screenNowDate.after(start)) {
                    // 判断下一条开始时间距离现在是否在半个小时之内，在，跳过当前
                    if (i != meetingVos.size() - 1) {
                        Date startDate = DateKit.getDateAndTimeCompose(meetingVos.get(i + 1).getMeetingDate(),
                            meetingVos.get(i + 1).getMeetingStartTime());
                        Date endDate = DateKit.getDateAndTimeCompose(meetingVos.get(i + 1).getMeetingDate(),
                            meetingVos.get(i + 1).getMeetingEndTime());
                        startDate = DateKit.addMinute(startDate, ConstantsInteger.MEETING_SCENE_BEFORE_TIME);
                        if (screenNowDate.before(endDate) && screenNowDate.after(startDate)) {
                            continue;
                        }
                    }
                    MeetingVo detail = getDetail(meetingVos.get(i).getMeetingId());
                    nowMeetingVos.add(detail);
                    if (i != meetingVos.size() - 1) {
                        MeetingVo meetingVo = getDetail(meetingVos.get(i + 1).getMeetingId());
                        nowMeetingVos.add(meetingVo);
                    }
                    break;
                }
            }
        }
        return AjaxResult.success(nowMeetingVos);
    }

    /**
     * 会议修改事件推送云屏app
     *
     * @param organizationId the organization id
     * @param spaceInfoId the space info id
     * @param spaceGroupUseType the space group use type
     * @return void
     * <AUTHOR>
     * @date 2022 /8/26 9:49
     */
    private void publishEventByMeetingModify(Long organizationId, Long spaceInfoId, Integer spaceGroupUseType) {
        if (spaceGroupUseType == null) {
            spaceGroupUseType = SpaceGroupUseType.XZ.getValue();
        }

        // 推送的设备
        List<String> deviceNumbers = Lists.newArrayList();
        // 行政教室地点
        if (spaceGroupUseType.equals(SpaceGroupUseType.XZ.getValue())) {
            ArrayList<Long> ids = new ArrayList<>();
            ids.add(spaceInfoId);
            List<ShowDeviceVo> showDeviceVos = showDeviceService.listBySpaceInfoIdsOfNotXz(ids);
            if (CollectionUtils.isNotEmpty(showDeviceVos)) {
                deviceNumbers
                    .addAll(showDeviceVos.stream().map(ShowDeviceVo::getDeviceNumber).collect(Collectors.toList()));
            }
        }
        if (spaceGroupUseType.equals(SpaceGroupUseType.NOT_XZ.getValue())) {
            ArrayList<Long> ids = new ArrayList<>();
            ids.add(spaceInfoId);
            List<ShowDeviceVo> showDeviceVos = showDeviceService.listBySpaceInfoIdsOfNotXz(ids);
            if (CollectionUtils.isNotEmpty(showDeviceVos)) {
                deviceNumbers
                    .addAll(showDeviceVos.stream().map(ShowDeviceVo::getDeviceNumber).collect(Collectors.toList()));
            }
        }

        // 地点绑定的设备不为空
        if (CollectionUtils.isNotEmpty(deviceNumbers)) {
            applicationContext.publishEvent(PublishEvent
                .produceMeetingPublishEvent(MessageWsType.MODIFY_MEETING.getValue(), organizationId, deviceNumbers));
        }
    }

}