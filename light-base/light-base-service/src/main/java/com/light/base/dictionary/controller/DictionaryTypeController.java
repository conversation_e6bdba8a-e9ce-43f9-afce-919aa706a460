package com.light.base.dictionary.controller;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.light.base.dictionary.api.DictionaryTypeApi;
import com.light.base.dictionary.entity.bo.DictionaryTypeBo;
import com.light.base.dictionary.entity.bo.DictionaryTypeListConditionBo;
import com.light.base.dictionary.entity.vo.DictionaryTypeVo;
import com.light.base.dictionary.service.IDictionaryTypeService;
import com.light.core.entity.AjaxResult;
import com.light.feign.annotation.FeignValidatorAnnotation;
import io.swagger.annotations.Api;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotNull;
import java.util.Map;

/**
 * 字典类型
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021-07-13 16:03:52
 */
@RestController
@Validated
@Api(value = "", tags = "字典类型接口")
public class DictionaryTypeController implements DictionaryTypeApi {

    @Autowired
    private IDictionaryTypeService dictionaryTypeService;

    /**
     * 查询字典类型列表
     * <AUTHOR>
     * @date 2021-07-13 16:03:52
     */
    @FeignValidatorAnnotation
    public AjaxResult getDictionaryTypeListByCondition(@RequestBody DictionaryTypeListConditionBo condition){
        PageHelper.startPage(condition.getPageNo(), condition.getPageSize(), condition.getOrderBy());
        PageInfo<DictionaryTypeVo> pageInfo = new PageInfo<>(dictionaryTypeService.getDictionaryTypeListByCondition(condition));
        return AjaxResult.success(pageInfo.getList(), pageInfo.getTotal(), condition.getPageNo(), condition.getPageSize());
    }

    /**
     * 查询所有字典类型列表
     * <AUTHOR>
     * @date 2021-07-13 16:03:52
     */
    @FeignValidatorAnnotation
    public AjaxResult getDictionaryTree(@RequestBody DictionaryTypeListConditionBo condition){
        return AjaxResult.success(dictionaryTypeService.getDictionaryTypeListByCondition(condition));
    }


    /**
     * 新增字典类型
     * <AUTHOR>
     * @date 2021-07-13 16:03:52
     */
    @FeignValidatorAnnotation
    public AjaxResult addDictionaryType(@Validated @RequestBody DictionaryTypeBo dictionaryTypeBo){
        return dictionaryTypeService.saveDictionaryType(dictionaryTypeBo);
    }

    /**
     * 查询字典类型详情
     * @param dictTypeId
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2021-07-13 16:03:52
     */
    @FeignValidatorAnnotation
    public AjaxResult getDetail(@NotNull(message = "请选择数据") Long dictTypeId) {
        Map<String, Object> map = dictionaryTypeService.getDetail(dictTypeId);
        return AjaxResult.success(map);
    }

}
