package com.light.base.statistics.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.light.base.statistics.entity.dto.UserDayStatisticsDto;
import com.light.base.statistics.entity.bo.UserDayStatisticsConditionBo;
import com.light.base.statistics.entity.bo.UserDayStatisticsBo;
import com.light.base.statistics.entity.vo.UserDayStatisticsVo;
import com.light.core.entity.AjaxResult;

import java.util.List;

/**
 * 用户每日访问元数据统计信息接口
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-12-16 10:21:49
 */
public interface IUserDayStatisticsService extends IService<UserDayStatisticsDto> {

    List<UserDayStatisticsVo> getUserDayStatisticsListByCondition(UserDayStatisticsConditionBo condition);

	AjaxResult addUserDayStatistics(UserDayStatisticsBo userDayStatisticsBo);

	AjaxResult updateUserDayStatistics(UserDayStatisticsBo userDayStatisticsBo);

	UserDayStatisticsVo getDetail(Long id);

    boolean batchSaveDynamicTable(List<UserDayStatisticsBo> userDayStatisticsBos);

    Long getSumViewDurationByCondition(UserDayStatisticsConditionBo condition);
}

