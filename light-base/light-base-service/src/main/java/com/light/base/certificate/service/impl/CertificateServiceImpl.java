package com.light.base.certificate.service.impl;

import cn.hutool.core.img.ImgUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.RandomUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.nacos.common.utils.CollectionUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.light.base.attachment.entity.dto.AttachmentDto;
import com.light.base.attachment.entity.vo.AttachmentVo;
import com.light.base.attachment.service.IAttachmentService;
import com.light.base.certificate.entity.CertificateStyle;
import com.light.base.certificate.entity.bo.CertificateGenerateBo;
import com.light.base.certificate.entity.vo.CertificateSignetVo;
import com.light.base.certificate.service.ICertificateSignetService;
import com.light.base.config.service.IConfigService;
import com.light.base.dictionary.service.IDictionaryDataService;
import com.light.base.utils.CertificateUtil;
import com.light.core.constants.SystemConstants;
import com.light.core.entity.Signet;
import com.light.core.utils.FuzzyQueryUtil;
import com.light.core.utils.SystemUtil;
import com.light.redis.enums.RedisKeyEnum;
import org.apache.commons.fileupload.FileItem;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.File;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.*;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import com.light.core.enums.StatusEnum;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import javax.annotation.Resource;
import javax.imageio.ImageIO;

import com.light.base.certificate.entity.dto.CertificateDto;
import com.light.base.certificate.entity.bo.CertificateConditionBo;
import com.light.base.certificate.entity.bo.CertificateBo;
import com.light.base.certificate.entity.vo.CertificateVo;
import com.light.base.certificate.service.ICertificateService;
import com.light.base.certificate.mapper.CertificateMapper;
import com.light.core.entity.AjaxResult;

import static org.bouncycastle.asn1.cmc.CMCStatus.failed;

/**
 * 证书接口实现类
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-03-30 13:58:25
 */
@Service
public class CertificateServiceImpl extends ServiceImpl<CertificateMapper, CertificateDto> implements ICertificateService {

	@Resource
	private CertificateMapper certificateMapper;

	@Resource
	private IAttachmentService attachmentService;

	@Value("${filepath.windows}")
	private String WINDOWS_PATH;

	@Value("${filepath.linux}")
	private String LINUX_PATH;

	@Resource
	private IDictionaryDataService dictionaryDataService;

	@Resource
	private IConfigService configService;

	@Resource
	private ICertificateSignetService certificateSignetService;

	@Autowired
	private RedissonClient redissonClient;

	private static final String LOCK_KEY = "certificate:start";

    @Override
	public List<CertificateVo> getCertificateListByCondition(CertificateConditionBo condition) {
		condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
		FuzzyQueryUtil.transferMeanBean(condition);
        return certificateMapper.getCertificateListByCondition(condition);
	}

	@Override
	public AjaxResult addCertificate(CertificateBo certificateBo) {
		CertificateDto certificate = new CertificateDto();
		BeanUtils.copyProperties(certificateBo, certificate);
		certificate.setIsDelete(StatusEnum.NOTDELETE.getCode());
		certificate.setOid(RandomUtil.randomString(SystemConstants.OID_LENGTH));
		if(!StringUtils.isEmpty(certificate.getBackground())) {//背景图片不为空
			AttachmentDto attachment = attachmentService.getByFileOid(certificateBo.getBackground());
			if(null == attachment || StringUtils.isEmpty(attachment.getFilePath())) {
				return AjaxResult.fail("证书背景图片不存在");
			}
			String path = SystemUtil.isWindows() ? WINDOWS_PATH : LINUX_PATH;
			BufferedImage image = ImgUtil.read(new File(path + attachment.getFilePath()));
			if(null == image) {
				return AjaxResult.fail("证书背景图片不存在");
			}
			certificate.setBackgroundWidth(image.getWidth());
			certificate.setBackgroundHeight(image.getHeight());
		}
		if(StatusEnum.NO.getCode().equals(certificate.getIsNumberCount())
				|| null == certificate.getIsNumberCount()) {// 是否有证书编号为空或者否，则编号相关内容设置为空
			certificate.setNumberCount(null);
			certificate.setNumberPrefix(null);
			certificate.setNumberStart(null);
		}
		if(save(certificate)){
			return AjaxResult.success("保存成功");
		}else{
			return AjaxResult.fail("保存失败");
		}
	}

	@Override
	public AjaxResult updateCertificate(CertificateBo certificateBo) {
		CertificateDto certificate = new CertificateDto();
		BeanUtils.copyProperties(certificateBo, certificate);
		if(!StringUtils.isEmpty(certificate.getBackground())) {//背景图片不为空
			AttachmentDto attachment = attachmentService.getByFileOid(certificateBo.getBackground());
			if(null == attachment || StringUtils.isEmpty(attachment.getFilePath())) {
				return AjaxResult.fail("证书背景图片不存在");
			}
			String path = SystemUtil.isWindows() ? WINDOWS_PATH : LINUX_PATH;
			BufferedImage image = ImgUtil.read(new File(path + attachment.getFilePath()));
			if(null == image) {
				return AjaxResult.fail("证书背景图片不存在");
			}
			certificate.setBackgroundWidth(image.getWidth());
			certificate.setBackgroundHeight(image.getHeight());
		}
		if(StatusEnum.NO.getCode().equals(certificateBo.getIsSignet())) {
			certificate.setSignet(null);
		}
		if(StatusEnum.NO.getCode().equals(certificate.getIsNumberCount())
					|| null == certificate.getIsNumberCount()) {// 是否有证书编号为空或者否，则编号相关内容设置为空
			certificate.setNumberCount(null);
			certificate.setNumberPrefix(null);
			certificate.setNumberStart(null);
		}
		if(updateById(certificate)){
			return AjaxResult.success("保存成功");
		}else{
			return AjaxResult.fail("保存失败");
		}
	}

	@Override
	public AjaxResult copy(Long id) {
    	//查询证书
		CertificateDto certificateDto = certificateMapper.selectById(id);
		if(null != certificateDto) {
			certificateDto.setId(null);
			certificateDto.setCurrentNumber(null);
			certificateDto.setSignet(null);
			certificateDto.setOid(RandomUtil.randomString(SystemConstants.OID_LENGTH));
			if(save(certificateDto)) {
				return AjaxResult.success("复制成功");
			}
			return AjaxResult.fail("复制失败");
		}
		return AjaxResult.fail("证书不存在");
	}

	@Override
	public CertificateVo getDetail(Long id) {
		CertificateConditionBo condition = new CertificateConditionBo();
		condition.setId(id);
		condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
		List<CertificateVo> list = certificateMapper.getCertificateListByCondition(condition);
		CertificateVo vo = new CertificateVo();
		if(!CollectionUtils.isEmpty(list)) {
			vo = list.get(0);
		}
		return vo;
	}

	@Override
	public CertificateDto getCertificateByOid(String oid) {
		LambdaQueryWrapper<CertificateDto> lqw = new LambdaQueryWrapper<CertificateDto>();
		lqw.eq(CertificateDto::getOid, oid);
		return certificateMapper.selectOne(lqw);
	}

	@Override
	public AjaxResult<String> priview(String oid){
		String prefix = configService.getConfigValue(SystemConstants.NGINX_FILE_URL).getData();
		//AjaxResult<AttachmentVo> result = drawCertificate(oid, "端木萍萍", "****", "*", true);//圆梦临时生成证书使用
		AjaxResult<AttachmentVo> result = drawCertificate(oid, "*********", "****", "*", true);
		if(result.isSuccess()) {
			AttachmentVo attachment = result.getData();
			String previewUrl = prefix + attachment.getFilePath();
			//更新预览地址
			LambdaUpdateWrapper<CertificateDto> luw = new LambdaUpdateWrapper<CertificateDto>();
			luw.set(CertificateDto::getPreviewUrl, previewUrl);
			luw.eq(CertificateDto::getOid, oid);
			update(luw);
			return AjaxResult.success(previewUrl);
		}else{
			return AjaxResult.fail("证书预览失败");
		}
	}

	@Override
	public AjaxResult<Map<String, Object>> generateSigle(String oid, String name, String award) {
		RLock lock = null;
		lock = redissonClient.getLock(LOCK_KEY);
		Map<String, Object> map = new HashMap<String, Object>();
		try {
			if(lock.tryLock(5l, 5l, TimeUnit.SECONDS)) {
				String prefix = configService.getConfigValue(SystemConstants.NGINX_FILE_URL).getData();
				Long startNumber = null;
				String number = null;
				//查询证书的当前编号
				CertificateDto certificateDto = this.getCertificateByOid(oid);
				if(null!=certificateDto.getNumberCount()) {// 证书编号位数不为空
					if(null == certificateDto.getCurrentNumber()) {// 证书当前的起始编号为空，则使用证书起始编号
						startNumber = certificateDto.getNumberStart();
						//设置下一次的起始编号
						certificateDto.setCurrentNumber(certificateDto.getNumberStart() + 1);
					}else{
						startNumber = certificateDto.getCurrentNumber();
						certificateDto.setCurrentNumber(certificateDto.getCurrentNumber() + 1);
					}
					// 计算编号是否够用（即下一次的起始编号大于最大编号）
					if(!CertificateUtil.isEnoughNumber(certificateDto.getNumberPrefix(), certificateDto.getNumberCount(),
							certificateDto.getCurrentNumber())) {
						if (null != lock && lock.isHeldByCurrentThread()) {
							lock.unlock();
						}
						return AjaxResult.fail("没有足够编号可以生成");
					}
					number = CertificateUtil.getCertificateNumber(certificateDto.getNumberPrefix(), certificateDto.getNumberCount(),
							startNumber);
				}

				AjaxResult<AttachmentVo> draw = generate(oid, name, award, number);
				if(draw.isSuccess()) {
					AttachmentVo attachment = draw.getData();
					map.put("fileOid", attachment.getFileOid());
					map.put("webUrl", prefix + attachment.getFilePath());
					map.put("number", number);
					map.put("certificateName", certificateDto.getName());
					//更新证书的下一次编号
					updateById(certificateDto);
				}
			}
		} catch (InterruptedException e) {
			if (null != lock && lock.isHeldByCurrentThread()) {
				lock.unlock();
			}
			log.error("生成证书错误{}", e);
			return AjaxResult.fail("生成失败");
		} finally {
			if (null != lock && lock.isHeldByCurrentThread()) {
				lock.unlock();
			}
			return AjaxResult.success(map);
		}
	}

	@Override
	public AjaxResult<AttachmentVo> generate(String oid, String name, String award, String number) {
		String prefix = configService.getConfigValue(SystemConstants.NGINX_FILE_URL).getData();
		AjaxResult<AttachmentVo> result = drawCertificate(oid, name, award, number, true);
		if(result.isSuccess()) {
			AttachmentVo attachment = result.getData();
			attachment.setWebUrl(prefix + attachment.getFilePath());
			return AjaxResult.success(attachment);
		}
		return result;
	}

	@Override
	public AjaxResult batchGenerate(CertificateGenerateBo bo) {
		RLock lock = null;
    	try {
			lock = redissonClient.getLock(LOCK_KEY);
			if(lock.tryLock(5l, 5l, TimeUnit.SECONDS)) {
				Long startNumber = null;
				//查询证书的当前编号
				CertificateDto certificateDto = this.getCertificateByOid(bo.getList().get(0).getCertificateOid());
				if(null!=certificateDto.getNumberCount()) {// 证书编号位数不为空
					if(null == certificateDto.getCurrentNumber()) {// 证书当前的起始编号为空，则使用证书起始编号
						startNumber = certificateDto.getNumberStart();
						//设置下一次的起始编号
						certificateDto.setCurrentNumber(certificateDto.getNumberStart() + bo.getList().size());
					}else{
						startNumber = certificateDto.getCurrentNumber();
						certificateDto.setCurrentNumber(certificateDto.getCurrentNumber() + bo.getList().size());
					}
					// 计算编号是否够用（即下一次的起始编号大于最大编号）
					if(!CertificateUtil.isEnoughNumber(certificateDto.getNumberPrefix(), certificateDto.getNumberCount(),
							certificateDto.getCurrentNumber())) {
						if (null != lock && lock.isHeldByCurrentThread()) {
							lock.unlock();
						}
						return AjaxResult.fail("没有足够编号可以生成");
					}
				}
				if(updateById(certificateDto)) {
					String prefix = configService.getConfigValue(SystemConstants.NGINX_FILE_URL).getData();
					bo.setPrefix(certificateDto.getNumberPrefix());
					bo.setNumberCount(certificateDto.getNumberCount());
					bo.setStartNumber(startNumber);
					bo.setCertificateName(certificateDto.getName());
					CertificateGenerateThread thread = new CertificateGenerateThread(bo, prefix);
					new Thread(thread).start();
				}else{
					if (null != lock && lock.isHeldByCurrentThread()) {
						lock.unlock();
					}
					return AjaxResult.fail("生成失败");
				}
			}
		}catch (Exception e) {
			if (null != lock && lock.isHeldByCurrentThread()) {
				lock.unlock();
			}
    		log.error("生成证书错误{}", e);
			return AjaxResult.fail("批量生成证书失败");
		} finally {
			if (null != lock && lock.isHeldByCurrentThread()) {
				lock.unlock();
			}
		}
		return AjaxResult.success(RedisKeyEnum.CERTIFICATE_BATCH_GEN_KEY.getValue() + bo.getKey());
	}

	private AjaxResult<AttachmentVo> drawCertificate(String oid, String objectName,
													 String award, String number, boolean isSave){
		AttachmentDto attachmentDto = new AttachmentDto();
		AttachmentVo attachmentVo  = new AttachmentVo();
		FileOutputStream outImgStream = null;
        File srcImgFile = null;
		try{
			String certificateStyle = configService.getConfigValue(SystemConstants.CERTIFICATE_STYLE).getData();
			CertificateStyle style = null;
			if(!StringUtils.isEmpty(certificateStyle)) {
				style = JSON.parseObject(certificateStyle, CertificateStyle.class);
			}else{
				style = new CertificateStyle();
			}
			CertificateDto certificate = this.getCertificateByOid(oid);
			if(null == certificate) {
				return AjaxResult.fail("证书不存在");
			}
			AttachmentDto attachment = attachmentService.getByFileOid(certificate.getBackground());
			if(null == attachment) {
				return AjaxResult.fail("证书背景图片不存在");
			}
			// 获取证书图片
			String path = SystemUtil.isWindows()?WINDOWS_PATH + attachment.getFilePath():LINUX_PATH + attachment.getFilePath();
			File file =new File(path);
			if(!file.exists()) {
				return AjaxResult.fail("证书背景图片不存在");
			}
			//复制一份图片
			String name = IdUtil.randomUUID().replaceAll("-","");
			String newFilePath = attachment.getFilePath().substring(0, attachment.getFilePath().lastIndexOf(File.separator))
					+ File.separator ;
			String newAbsFilePath = SystemUtil.isWindows()?WINDOWS_PATH + newFilePath : LINUX_PATH + newFilePath;
            srcImgFile = File.createTempFile(name, SystemConstants.SEPERATOR_POINT + attachment.getFileExtName());
			FileUtils.copyFile(file, srcImgFile);
			Image srcImg = ImageIO.read(srcImgFile);//文件转化为图片
			// 加水印
			BufferedImage bufImg = new BufferedImage(certificate.getBackgroundWidth(), certificate.getBackgroundHeight(), BufferedImage.TYPE_INT_RGB);
			Graphics2D g = bufImg.createGraphics();
			g.drawImage(srcImg, 0, 0, certificate.getBackgroundWidth(), certificate.getBackgroundHeight(), null);
			g.setColor(Color.black); //根据图片的背景设置水印颜色
			// 消除锯齿
			g.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
			if(!StringUtils.isEmpty(number) && null != certificate.getNumberCount()) {// 画证书编号
				if("*".equals(number)) {
					CertificateUtil.setCertificateNumber(style, g, certificate.getBackgroundWidth(),
							"证书编号：" + CertificateUtil.getCertificateNumber(certificate.getNumberPrefix()
									, certificate.getNumberCount(), certificate.getNumberStart()));
				}else{
					CertificateUtil.setCertificateNumber(style, g, certificate.getBackgroundWidth(),
							"证书编号：" + number);
				}
			}
			if(!StringUtils.isEmpty(certificate.getType())) {
				String title = dictionaryDataService.getDictValueByCode("certificate_type", certificate.getType());
				if(!StringUtils.isEmpty(title)) {
					CertificateUtil.setCertificateTitle(style, g, title, certificate.getBackgroundWidth());
				}
			}
			//设置对象
			String obejct =(StringUtils.isEmpty(certificate.getObject())?"：":certificate.getObject());
			//String obejct =(StringUtils.isEmpty(certificate.getObject())?"":certificate.getObject());//圆梦临时生成证书使用
			CertificateUtil.setObject(style, g,objectName, obejct);
			//设置内容
			if(!StringUtils.isEmpty(certificate.getContent())) {
				CertificateUtil.setContent(style, g, certificate.getContent(), certificate.getBackgroundWidth());
			}
			//设置奖项
			if(StatusEnum.YES.getCode().equals(certificate.getIsShowAward())) {
				//获取内容高度
				int contentHeight = CertificateUtil.getContentHeight(style, certificate.getContent(),
						certificate.getBackgroundWidth());
				//距离顶部距离为内容到顶部距离加上内容的高度
				CertificateUtil.setAward(style, g, award, contentHeight);
			}
			//设置文本
			if(!StringUtils.isEmpty(certificate.getText())) {
				int awardHeight = CertificateUtil.getAwardHeight(style, certificate.getContent(),
						certificate.getBackgroundWidth(), StatusEnum.YES.getCode().equals(certificate.getIsShowAward()));
				//距离顶部距离为奖项到顶部距离(如果有奖项加上奖项的高度)
				CertificateUtil.setText(style, g, certificate.getText(), awardHeight);
			}
			//设置发证单位
			if(!StringUtils.isEmpty(certificate.getIssuingUnit())) {
				int textHeight = CertificateUtil.getTextHeight(style, certificate.getContent(),
						certificate.getBackgroundWidth(), StatusEnum.YES.getCode().equals(certificate.getIsShowAward()),
						!StringUtils.isEmpty(certificate.getText()));
				//距离顶部距离为奖项到顶部距离(如果有奖项加上奖项的高度)
				String[] units = certificate.getIssuingUnit().split(SystemConstants.SEPERATOR_COMMA);
				CertificateUtil.setIssuingUnit(style, g, units, certificate.getBackgroundWidth(), textHeight);
			}
			//设置发证日期
			if(!StringUtils.isEmpty(certificate.getIssuingDate())) {
				int units = !StringUtils.isEmpty(certificate.getIssuingUnit())
						?certificate.getIssuingUnit().split(SystemConstants.SEPERATOR_COMMA).length:0;
				int unitHeight = CertificateUtil.getIssuingUnitHeight(style, certificate.getContent(),
						certificate.getBackgroundWidth(), StatusEnum.YES.getCode().equals(certificate.getIsShowAward()),
						!StringUtils.isEmpty(certificate.getText()), units);
				CertificateUtil.setIssuingDate(style, g, certificate.getIssuingDate(), certificate.getBackgroundWidth(),unitHeight );
			}
			//设置印章
			if(!StringUtils.isEmpty(certificate.getSignet())) {
				//查询水印图片
				List<CertificateSignetVo> signets = certificateSignetService.getCertificateSignetsByIds(certificate.getSignet());
				if(!CollectionUtils.isEmpty(signets)) {
					List<String> fileOids = new ArrayList<String>();
					signets.stream().forEach(x -> {
						if(!StringUtils.isEmpty(x.getFile())) {
							fileOids.add(x.getFile());
						}
					});
					if(!CollectionUtils.isEmpty(fileOids)) {
						List<AttachmentVo> attachments = attachmentService.getByFileOids(fileOids);
						List<Signet> list = signets.stream()
								.map( signet -> attachments.stream()
										.filter(vo -> vo.getFileOid().equals(signet.getFile()))
										.findFirst()
										.map(vo -> {
											Signet s = new Signet();
											s.setFilePath(SystemUtil.isWindows()?WINDOWS_PATH + vo.getFilePath():LINUX_PATH + vo.getFilePath());
											s.setWidth(signet.getWidth());
											s.setHeight(signet.getHeight());
											return s;
										}).orElse(null)).collect(Collectors.toList());
						int textHeight = CertificateUtil.getTextHeight(style, certificate.getContent(),
								certificate.getBackgroundWidth(), StatusEnum.YES.getCode().equals(certificate.getIsShowAward()),
								!StringUtils.isEmpty(certificate.getText()));
						int lines = 0;
						if(!StringUtils.isEmpty(certificate.getIssuingUnit())) {
							lines = lines + certificate.getIssuingUnit().split(SystemConstants.SEPERATOR_COMMA).length;
						}
						if(!StringUtils.isEmpty(certificate.getIssuingDate())) {
							lines = lines + 1;
						}
						CertificateUtil.setSignets(style, g, list, certificate.getBackgroundWidth(), textHeight, lines);
					}
				}
			}
			g.dispose();
			// 输出图片
			String uuid = IdUtil.randomUUID().replaceAll("-", "");
			outImgStream = new FileOutputStream(newAbsFilePath + uuid + SystemConstants.SEPERATOR_POINT + attachment.getFileExtName());
			ImageIO.write(bufImg, attachment.getFileExtName(), outImgStream);
			outImgStream.flush();
			outImgStream.close();
			File newImage = new File(newAbsFilePath + uuid + SystemConstants.SEPERATOR_POINT + attachment.getFileExtName());
			long size = newImage.length();
			// 封装附件对象
			attachmentDto.setFileOid(IdUtil.randomUUID().replaceAll("-", ""));
			attachmentDto.setNewName(uuid + SystemConstants.SEPERATOR_POINT + attachment.getFileExtName());
			attachmentDto.setFilePath(newFilePath + uuid + SystemConstants.SEPERATOR_POINT + attachment.getFileExtName());
			attachmentDto.setPreviewPath(attachmentDto.getFilePath());
			attachmentDto.setFileExtName(attachment.getFileExtName());
			attachmentDto.setFileSize(size);
			attachmentDto.setFileType("other");
			attachmentDto.setOriginalName("证书"+ SystemConstants.SEPERATOR_POINT + attachment.getFileExtName());
			if(isSave) {
				attachmentService.save(attachmentDto);
			}
			BeanUtils.copyProperties(attachmentDto, attachmentVo);
		}catch (IOException e) {
			if(null != outImgStream) {
				try {
					outImgStream.flush();
					outImgStream.close();
				} catch (IOException ioException) {
					log.error("关闭文件流失败");
				}
			}
			log.error("生成图片错误{}", e);
			return AjaxResult.fail("生成图片错误");
		}finally {
            // 删除临时文件
            if(srcImgFile != null && srcImgFile.exists()){
                FileUtil.del(srcImgFile);
            }
        }
		return AjaxResult.success(attachmentVo);
	}

}
