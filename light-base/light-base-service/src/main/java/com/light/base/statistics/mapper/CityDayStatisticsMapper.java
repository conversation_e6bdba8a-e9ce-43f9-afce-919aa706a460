package com.light.base.statistics.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.light.base.statistics.entity.dto.CityDayStatisticsDto;
import com.light.base.statistics.entity.bo.CityDayStatisticsConditionBo;
import com.light.base.statistics.entity.vo.CityDayStatisticsVo;

/**
 * 以学段为最小粒度 每日PV UV统计信息Mapper
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-12-16 10:21:49
 */
public interface CityDayStatisticsMapper extends BaseMapper<CityDayStatisticsDto> {

	List<CityDayStatisticsVo> getCityDayStatisticsListByCondition(CityDayStatisticsConditionBo condition);

    List<CityDayStatisticsVo> getListByCondForGroupCity(CityDayStatisticsConditionBo bo);

}
