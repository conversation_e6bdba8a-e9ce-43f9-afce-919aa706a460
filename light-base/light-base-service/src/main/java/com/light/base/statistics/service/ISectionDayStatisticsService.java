package com.light.base.statistics.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.light.base.statistics.entity.dto.SectionDayStatisticsDto;
import com.light.base.statistics.entity.bo.SectionDayStatisticsConditionBo;
import com.light.base.statistics.entity.bo.SectionDayStatisticsBo;
import com.light.base.statistics.entity.vo.SectionDayStatisticsVo;
import com.light.core.entity.AjaxResult;

import java.util.List;

/**
 * 以城市为最小粒度 每日PV UV统计信息接口
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-12-16 10:21:49
 */
public interface ISectionDayStatisticsService extends IService<SectionDayStatisticsDto> {

    List<SectionDayStatisticsVo> getSectionDayStatisticsListByCondition(SectionDayStatisticsConditionBo condition);

	AjaxResult addSectionDayStatistics(SectionDayStatisticsBo sectionDayStatisticsBo);

	AjaxResult updateSectionDayStatistics(SectionDayStatisticsBo sectionDayStatisticsBo);

	SectionDayStatisticsVo getDetail(Long id);

    List<SectionDayStatisticsVo> getListByCondForGroupSection(SectionDayStatisticsConditionBo bo);
}

