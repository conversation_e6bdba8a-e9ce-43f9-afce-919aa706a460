package com.light.base.order.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.light.base.order.entity.bo.OrderBo;
import com.light.base.order.entity.bo.OrderConditionBo;
import com.light.base.order.entity.dto.OrderDto;
import com.light.base.order.entity.vo.OrderVo;
import com.light.core.entity.AjaxResult;

import java.util.List;

/**
 * 订单表接口
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-11-09 10:33:22
 */
public interface IOrderService extends IService<OrderDto> {

    List<OrderVo> getOrderListByCondition(OrderConditionBo condition);

	AjaxResult addOrder(OrderBo orderBo);

	AjaxResult updateOrder(OrderBo orderBo);

	OrderVo getOrderByCondition(OrderConditionBo condition);

    String getStatusByOrderNo(String orderNo);

    OrderVo getOrderVoByOrderNo(String orderNo);

    boolean updateByOrderNo(OrderBo bo);

    AjaxResult refunds(OrderBo bo);

    /**
     * 批量新增
     *
     * @param orderBo
     * @return {@link AjaxResult}<{@link List}<{@link OrderVo}>>
     */
    AjaxResult<List<OrderVo>> batchAddOrder(List<OrderBo> orderBo);

    AjaxResult cancelExpireOrder();

}

