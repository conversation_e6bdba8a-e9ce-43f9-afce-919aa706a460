package com.light.activity.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @since 2023/11/7
 */

public interface SignupInfoEnums {


    @Getter
    @AllArgsConstructor
    enum RelationType{
        ACTIVITY(1, "活动");

        private final Integer val;

        private final String remark;
    }

    /**
     *  报名状态（0 报名中 1：报名成功，2：取消报名中，3：已取消）
     */
    @Getter
    @AllArgsConstructor
    enum State{

        SIGN_ING(0, "报名中"),
        SIGNED(1, "已报名"),
        CANCEL_ING(2, "取消中"),
        CANCELED(3, "已取消"),

        ;

        private final int val;

        private final String remark;

    }


    /**
     * 聚合支付方式
     */
    @Getter
    @AllArgsConstructor
    enum AggregationPayModel{

        FREE("平台赠送"),
        BUSINESS( "对公汇款"),
        SITE("现场缴费"),

        ;


        private final String val;


        public static String[] getValList(){
            return Arrays.stream(values()).map(AggregationPayModel::getVal).toArray(String[]::new);
        }
    }


    /**
     * 支付方式
     */
    @Getter
    @AllArgsConstructor
    enum PayStatusEnums {

        //支付状态： 1 待支付  2 已支付  3 未支付  4 待退款 5 退款中 6 已退款 7 退款失败 8 已取消

        WAIT_PAY(1, "待支付"),
        PAYED(2, "已支付"),
        UN_PAY(3, "未支付"),
        WAIT_REFUNDS(4, "待退款"),
        REFUNDS_ING(5, "退款中"),
        REFUNDED(6, "已退款"),
        REFUNDS_FAIL(7, "退款失败"),
        CANCEL(8, "已取消"),


        ;

        private final int val;

        private final String remark;


        public static String getRemark(int val){
            return Arrays.stream(values()).filter(x-> x.getVal() == val).map(PayStatusEnums::getRemark).findFirst().orElse("");
        }
    }


    /**
     * 支付方式
     */
    @Getter
    @AllArgsConstructor
    enum PayModeEnums {

        //支付方式：1 微信  2 支付宝 3 对公账号 4 线下付款

        FREE(-99, "免费"),
        WX_PAY(1, "微信支付"),
        ALI_PAY(2, "支付宝支付"),
        BUSINESS(3, "对公汇款"),
        SITE(4, "现场缴费"),


        ;

        private final int val;

        private final String remark;

        public static String getRemark(int val){
            return Arrays.stream(values()).filter(x-> x.getVal() == val).map(PayModeEnums::getRemark).findFirst().orElse("");
        }
    }

    /**
     * 支付类型
     */
    @Getter
    @AllArgsConstructor
    enum PayTypeEnums {

        //支付类型：1 线上支付  2 线下支付  3 免费

        ONLINE(1, "线上支付"),
        OFFLINE(2, "线下支付"),
        FREE(3, "免费"),


        ;

        private final int val;

        private final String remark;
    }


    /**
     *  开票状态 开票状态： 1 待申请 2 申请中 3 申请成功 4 申请失败
     */
    @Getter
    @AllArgsConstructor
    enum InvoiceStatusEnums {

        //开票状态：-95 作废失败 -96 作废中 -97 开票失败 -98  已作废  -99 已取消 1 待申请 2 已提交 3 申请中 4 申请成功

        WAIT(1, "待申请"),
        COMMITTED(2, "已提交"),
        ING(3, "申请中"),
        SUCCESS(4, "申请成功"),
        FAIL(-97, "申请失败"),

        OBSOLETE(-98, "已作废"),
        OBSOLETE_ING(-96, "作废中"),
        OBSOLETE_FAIL(-95,"作废失败"),

        CANCEL( -99, "已取消"),


        ;

        private final int val;

        private final String remark;
    }


}
