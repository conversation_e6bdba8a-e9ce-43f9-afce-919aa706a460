package com.light.user.user.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.light.user.user.entity.bo.UserRoleDelSaveBo;
import com.light.user.user.entity.dto.UserRoleDto;
import com.light.user.user.entity.bo.UserRoleConditionBo;
import com.light.user.user.entity.bo.UserRoleBo;
import com.light.user.user.entity.vo.UserRoleVo;
import com.light.core.entity.AjaxResult;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;

/**
 * 平台用户角色表接口
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-03-02 14:01:49
 */
public interface IUserRoleService extends IService<UserRoleDto> {

    List<UserRoleVo> getUserRoleListByCondition(UserRoleConditionBo condition);

	AjaxResult addUserRole(UserRoleBo userRoleBo);

	AjaxResult updateUserRole(UserRoleBo userRoleBo);

	Map<String, Object> getDetail(Long userRoleId);

    List<UserRoleVo> getByUserOid(String userOid);

    AjaxResult deleteByCondition(UserRoleBo userRoleBo);

    /**
     *
     * @param roleId
     * @param orgId
     * @param userRoleBos
     * @return
     */
    AjaxResult delAndSaveByRoleId(Long roleId, Long orgId, List<UserRoleBo> userRoleBos);

    /**
     *
     * @param userOid
     * @param orgId
     * @param userRoleBos
     * @return
     */
    AjaxResult delAndSaveByUserOid(String userOid, Long orgId, List<UserRoleBo> userRoleBos);

    /**
     *
     *
     * @param userRoleDelSaveBo@return
     */
    AjaxResult delAndSaveByUserOidAndRoleIds(UserRoleDelSaveBo userRoleDelSaveBo);
}

