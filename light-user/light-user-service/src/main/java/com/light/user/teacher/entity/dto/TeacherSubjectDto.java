package com.light.user.teacher.entity.dto;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 教师任教科目
 * 
 * <AUTHOR>
 * @email ${email}
 * @date 2022-03-14 11:05:42
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("p_teacher_subject")
public class TeacherSubjectDto implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 主键
	 */
	@TableId(value = "id", type = IdType.AUTO)
	private Long id;

	/**
	 * 
	 */
	@TableField("teacher_user_oid")
	private String teacherUserOid;

	/**
	 * 
	 */
	@TableField("subject_code")
	private String subjectCode;

	/**
	 * 
	 */
	@TableField("create_by")
	private String createBy;

	/**
	 * 
	 */
	@TableField("create_time")
	private Date createTime;

	/**
	 * 
	 */
	@TableField("update_by")
	private String updateBy;

	/**
	 * 
	 */
	@TableField("update_time")
	private Date updateTime;

	/**
	 * 
	 */
	@TableField("is_delete")
	private Integer isDelete;

}
