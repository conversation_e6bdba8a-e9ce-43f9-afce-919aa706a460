<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.light.user.user.mapper.UserPlatformInfoMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.light.user.user.entity.dto.UserPlatformInfoDto" id="BaseResultMap">
        <result property="id" column="id"/>
        <result property="userOid" column="user_oid"/>
        <result property="type" column="type"/>
        <result property="platformUserId" column="platform_user_id"/>
        <result property="remark" column="remark"/>
        <result property="isDelete" column="is_delete"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

	<sql id="common_where">
		<where>
			<if test="id != null ">and id = #{id}</if>
			<if test="userOid != null and userOid != '' ">and user_oid = #{userOid}</if>
			<if test="type != null ">and type = #{type}</if>
			<if test="platformUserId != null and platformUserId != '' ">and platform_user_id = #{platformUserId}</if>
			<if test="remark != null and remark != '' ">and remark like concat('%',#{platformUserId},'%')</if>
			<if test="isDelete != null ">and is_delete = #{isDelete}</if>
		</where>
	</sql>

	<sql id="table_where">
		<where>
			<if test="id != null ">and id = #{id}</if>
			<if test="userOid != null and userOid != '' ">and user_oid = #{userOid}</if>
			<if test="type != null ">and type = #{type}</if>
            <if test="platformUserId != null and platformUserId != '' ">and platform_user_id = #{platformUserId}</if>
            <if test="remark != null and remark != '' ">and remark like concat('%',#{platformUserId},'%')</if>
			<if test="isDelete != null ">and is_delete = #{isDelete}</if>
		</where>
	</sql>

	<sql id="common_select">
		select
			t.id
	 		,t.user_oid
	 		,t.type
	 		,t.platform_user_id
	 		,t.is_delete
	 		,t.create_time
	 		,t.update_time
		from (
			select a.* from p_user_platform_info a
			<include refid="table_where"></include>
		 ) t

	</sql>

	<select id="getUserPlatformInfoListByCondition" resultType="com.light.user.user.entity.vo.UserPlatformInfoVo">
		<include refid="common_select"></include>
		<include refid="common_where"></include>
	</select>

	<select id="getUserPlatformInfoByCondition" resultType="com.light.user.user.entity.vo.UserPlatformInfoVo">
		<include refid="common_select"></include>
		<include refid="common_where"></include>
	</select>
</mapper>
