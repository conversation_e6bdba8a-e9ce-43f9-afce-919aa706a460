package com.light.user.article.entity.bo;

import com.light.core.entity.PageLimitBo;

import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 文章学习记录
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-04-11 15:28:09
 */
@Data
public class UserArticleStudyRecordConditionBo extends PageLimitBo{

	private static final long serialVersionUID = 1L;

	/**
	 *
	 */
	@ApiModelProperty("")
	private Long id;

	/**
	 * 用户OID
	 */
	@ApiModelProperty("用户OID")
	private String userOid;

	/**
	 * 文章ID
	 */
	@ApiModelProperty("文章ID")
	private Long articleId;

	/**
	 * 类型，1：阅读三十秒，2：文章底部
	 */
	@ApiModelProperty("类型，1：阅读三十秒，2：文章底部")
	private Integer type;

	/**
	 * 学习时长
	 */
	@ApiModelProperty("学习时长")
	private Long studyDuration;


	/**
	 * 是否删除0 否 1 是
	 */
	@ApiModelProperty("是否删除0 否 1 是")
	private Integer isDelete;

    /**
     * 开始时间
     */
    @ApiModelProperty("开始时间")
    private Date startTime;

    /**
     * 结束时间
     */
    @ApiModelProperty("结束时间")
    private Date endTime;

}
