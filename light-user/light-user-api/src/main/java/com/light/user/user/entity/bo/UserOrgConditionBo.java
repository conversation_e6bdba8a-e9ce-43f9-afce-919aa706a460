package com.light.user.user.entity.bo;

import com.light.core.entity.PageLimitBo;

import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import lombok.Data;

/**
 * 平台用户学校表
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-03-02 14:02:37
 */
@Data
public class UserOrgConditionBo extends PageLimitBo{

	/**
	 * 
	 */
	@ApiModelProperty("")
	private Long id;

	/**
	 * 
	 */
	@ApiModelProperty("")
	private String userOid;

	/**
	 * 所属组织ID
	 */
	@ApiModelProperty("所属组织ID")
	private Long organizationId;

	/**
	 * 
	 */
	@ApiModelProperty("")
	private String createBy;

	/**
	 * 
	 */
	@ApiModelProperty("")
	private Date createTime;

	/**
	 * 
	 */
	@ApiModelProperty("")
	private Date updateTime;

	/**
	 * 
	 */
	@ApiModelProperty("")
	private String updateBy;

}
