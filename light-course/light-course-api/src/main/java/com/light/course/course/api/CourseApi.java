package com.light.course.course.api;


import com.light.course.course.entity.bo.CourseConditionBo;
import com.light.course.course.entity.bo.CourseBo;
import com.light.course.course.entity.vo.CourseVo;

import com.light.core.entity.AjaxResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import com.github.pagehelper.PageInfo;
import javax.validation.constraints.NotNull;
import java.util.List;
/**
 * 课程
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-06-24 15:58:04
 */
public interface CourseApi {

    /**
     * 查询课程分页列表
     * <AUTHOR>
     * @date 2022-06-24 15:58:04
     */
    @PostMapping("/course/page/list")
    public AjaxResult<PageInfo<CourseVo>> getCoursePageListByCondition(@RequestBody CourseConditionBo condition);

    /**
     * 查询课程列表
     * <AUTHOR>
     * @date 2022-06-24 15:58:04
     */
    @PostMapping("/course/list")
    public AjaxResult<List<CourseVo>> getCourseListByCondition(@RequestBody CourseConditionBo condition);


    /**
     * 新增课程
     * <AUTHOR>
     * @date 2022-06-24 15:58:04
     */
    @PostMapping("/course/add")
    public AjaxResult addCourse(@Validated @RequestBody CourseBo courseBo);

    /**
     * 修改课程
     * @param courseBo
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-06-24 15:58:04
     */
    @PostMapping("/course/update")
    public AjaxResult updateCourse(@Validated @RequestBody CourseBo courseBo);

    /**
     * 查询课程详情
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-06-24 15:58:04
     */
    @GetMapping("/course/detail")
    public AjaxResult<CourseVo> getDetail(@NotNull(message = "请选择数据") @RequestParam("id") Long id);

    /**
     * 删除课程
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-06-24 15:58:04
     */
    @GetMapping("/course/delete")
    public AjaxResult delete(@NotNull(message = "请选择数据") @RequestParam("id") Long id);
}
