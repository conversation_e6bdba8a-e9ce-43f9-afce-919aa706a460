package com.fh.cp.service.cp.entity.bo;

import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.Date;

import lombok.Data;

/**
 * 课程拓展信息_课程说明
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2023-05-29 14:56:03
 */
@Data
public class CpCourseExtIllustrateBo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键，无特殊含义
     */
    @ApiModelProperty("主键，无特殊含义")
    private Long id;

    /**
     * 课程id
     */
    @ApiModelProperty("课程id")
    private Long courseId;

    /**
     * 说明类型：1课程介绍，2课时计划，3课程资源，4费用说明
     */
    @ApiModelProperty("说明类型：1课程介绍，2课时计划，3课程资源，4费用说明")
    private Integer illustrateType;

    /**
     * 说明字段的顺序，默认1
     */
    @ApiModelProperty("说明字段的顺序，默认1")
    private Long illustrateIndex;

    /**
     * 标题
     */
    @ApiModelProperty("标题")
    private String illustrateTitle;

    /**
     * 内容
     */
    @ApiModelProperty("内容")
    private String illustrateContent;

    /**
     * 文件fileOid
     */
    @ApiModelProperty("文件fileOid")
    private String illustrateMediaId;

    /**
     * 媒体类型：1文档，2图片，3视频，4音频，5压缩包，6其他
     */
    @ApiModelProperty("媒体类型：1文档，2图片，3视频，4音频，5压缩包，6其他")
    private Integer illustrateMediaType;

    /**
     * 媒体文件后缀，小写
     */
    @ApiModelProperty("媒体文件后缀，小写")
    private String illustrateMediaSuffix;

    /**
     * 文件地址
     */
    @ApiModelProperty("文件地址")
    private String illustrateMediaUrl;

    /**
     * 文档文件地址-压缩后地址
     */
    @ApiModelProperty("文档文件地址-压缩后地址")
    private String illustrateMediaUrlCompress;

    /**
     * 文件名称，不带后缀
     */
    @ApiModelProperty("文件名称，不带后缀")
    private String illustrateMediaName;

    /**
     * 文件名称，带后缀
     */
    @ApiModelProperty("文件名称，带后缀")
    private String illustrateMediaNameOri;

    /**
     * 文件转码状态：1未转码，2已转码，3转码失败
     */
    @ApiModelProperty("文件转码状态：1未转码，2已转码，3转码失败")
    private Integer illustrateMediaTransferType;

    /**
     * 是否删除，0：否，1：是
     */
    @ApiModelProperty("是否删除，0：否，1：是")
    private Integer isDelete;

    /**
     * 压缩文件文件fileOid
     */
    private String illustrateMediaIdCompress;

    /**
     * 资源封面url
     */
    @ApiModelProperty("资源封面url")
    private String illustrateMediaCoverUrl;
}
