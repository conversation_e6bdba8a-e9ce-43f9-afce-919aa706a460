package com.fh.cp.service.cp.entity.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2023-07-10 9:56
 */
@Data
public class CpUserOrganizationVo {

    /**
     * 组织id
     */
    @ApiModelProperty("组织id")
    private Long organizationId;

    /**
     * 组织名称
     */
    @ApiModelProperty("组织名称")
    private String name;

    /**
     * 组织类型 1-机构 2-学校 3-第三方机构
     */
    @ApiModelProperty("组织类型")
    private Integer type;

    /**
     * 组织封面
     */
    @ApiModelProperty("组织机构封面")
    private String cover;

    /**
     * 父级组织id
     */
    @ApiModelProperty("父级组织id")
    private Long parentId;

    /**
     * 父级组织名称
     */
    @ApiModelProperty("父级组织名称")
    private String parentName;

    /**
     * 地址
     */
    @ApiModelProperty("地址")
    private String address;

    /**
     * 网站地址
     */
    @ApiModelProperty("网站地址")
    private String website;

    /**
     * 类别
     */
    @ApiModelProperty("类别")
    private String categoryIds;

    /**
     * 介绍
     */
    @ApiModelProperty("介绍")
    private String introduction;

    /**
     * 图标图片文件url
     */
    @ApiModelProperty("图标图片文件url")
    private String institutionIconUrl;

    /**
     * 营业执照图片文件fileOid
     */
    @ApiModelProperty("营业执照图片文件fileOid")
    private String institutionLicenseId;

    /**
     * 营业执照图片文件url
     */
    @ApiModelProperty("营业执照图片文件url")
    private String institutionLicenseUrl;

    /**
     * 社会信用代码
     */
    @ApiModelProperty("社会信用代码")
    private String institutionLicenseCode;

    /**
     * 机构法人
     */
    @ApiModelProperty("机构法人")
    private String institutionMaster;

    /**
     * 联系人姓名
     */
    @ApiModelProperty("联系人姓名")
    private String institutionConnectUser;

    /**
     * 联系人手机号
     */
    @ApiModelProperty("联系人手机号")
    private String institutionConnectMobile;
}
