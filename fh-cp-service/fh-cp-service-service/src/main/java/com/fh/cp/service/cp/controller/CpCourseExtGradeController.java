package com.fh.cp.service.cp.controller;

import com.fh.cp.service.cp.api.CpCourseExtGradeApi;
import com.fh.cp.service.cp.entity.dto.CpCourseExtGradeDto;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import com.fh.cp.service.cp.entity.bo.CpCourseExtGradeConditionBo;
import com.fh.cp.service.cp.entity.bo.CpCourseExtGradeBo;
import com.fh.cp.service.cp.entity.vo.CpCourseExtGradeVo;
import com.fh.cp.service.cp.service.ICpCourseExtGradeService;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.light.core.entity.AjaxResult;
import com.light.core.enums.StatusEnum;

import java.util.List;

/**
 * 课程扩展信息_适用年级表
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2023-05-29 14:56:21
 */
@RestController
@Validated
public class CpCourseExtGradeController implements CpCourseExtGradeApi {

    @Autowired
    private ICpCourseExtGradeService cpCourseExtGradeService;

    /**
     * 查询课程扩展信息_适用年级表分页列表
     *
     * <AUTHOR>
     * @date 2023-05-29 14:56:21
     */
    @Override
    public AjaxResult<PageInfo<CpCourseExtGradeVo>>
        getCpCourseExtGradePageListByCondition(@RequestBody CpCourseExtGradeConditionBo condition) {
        PageHelper.startPage(condition.getPageNo(), condition.getPageSize(), condition.getOrderBy());
        PageInfo<CpCourseExtGradeVo> pageInfo =
            new PageInfo<>(cpCourseExtGradeService.getCpCourseExtGradeListByCondition(condition));
        return AjaxResult.success(pageInfo);
    }

    /**
     * 查询课程扩展信息_适用年级表列表
     *
     * <AUTHOR>
     * @date 2023-05-29 14:56:21
     */
    @Override
    public AjaxResult<List<CpCourseExtGradeVo>>
        getCpCourseExtGradeListByCondition(@RequestBody CpCourseExtGradeConditionBo condition) {
        List<CpCourseExtGradeVo> list = cpCourseExtGradeService.getCpCourseExtGradeListByCondition(condition);
        return AjaxResult.success(list);
    }

    /**
     * 新增课程扩展信息_适用年级表
     *
     * <AUTHOR>
     * @date 2023-05-29 14:56:21
     */
    @Override
    public AjaxResult addCpCourseExtGrade(@Validated @RequestBody CpCourseExtGradeBo cpCourseExtGradeBo) {
        return cpCourseExtGradeService.addCpCourseExtGrade(cpCourseExtGradeBo);
    }

    /**
     * 修改课程扩展信息_适用年级表
     *
     * @param cpCourseExtGradeBo
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-05-29 14:56:21
     */
    @Override
    public AjaxResult updateCpCourseExtGrade(@Validated @RequestBody CpCourseExtGradeBo cpCourseExtGradeBo) {
        if (null == cpCourseExtGradeBo.getId()) {
            return AjaxResult.fail("课程扩展信息_适用年级表id不能为空");
        }
        return cpCourseExtGradeService.updateCpCourseExtGrade(cpCourseExtGradeBo);
    }

    /**
     * 查询课程扩展信息_适用年级表详情
     *
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-05-29 14:56:21
     */
    @Override
    public AjaxResult<CpCourseExtGradeVo> getDetail(@RequestParam("id") Long id) {
        if (null == id) {
            return AjaxResult.fail("课程扩展信息_适用年级表id不能为空");
        }
        CpCourseExtGradeConditionBo condition = new CpCourseExtGradeConditionBo();
        condition.setId(id);
        CpCourseExtGradeVo vo = cpCourseExtGradeService.getCpCourseExtGradeByCondition(condition);
        return AjaxResult.success(vo);
    }

    /**
     * 删除课程扩展信息_适用年级表
     *
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-05-29 14:56:21
     */
    @Override
    public AjaxResult delete(@RequestParam("id") Long id) {
        if (null == id) {
            return AjaxResult.fail("请选择需要删除的数据");
        }
        CpCourseExtGradeDto cpCourseExtGradeDto = new CpCourseExtGradeDto();
        cpCourseExtGradeDto.setId(id);
        cpCourseExtGradeDto.setIsDelete(StatusEnum.ISDELETE.getCode());
        if (cpCourseExtGradeService.updateById(cpCourseExtGradeDto)) {
            return AjaxResult.success("删除成功");
        }
        return AjaxResult.fail("删除失败");
    }

}
