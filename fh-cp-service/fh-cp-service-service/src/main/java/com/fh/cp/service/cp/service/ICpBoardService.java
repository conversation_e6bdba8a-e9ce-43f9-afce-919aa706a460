package com.fh.cp.service.cp.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fh.cp.service.cp.entity.bo.CpBoardBo;
import com.fh.cp.service.cp.entity.dto.CpCourseDto;
import com.light.core.entity.AjaxResult;


/**
 * 课程表接口
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2023-05-29 14:56:03
 */
public interface ICpBoardService extends IService<CpCourseDto> {

    AjaxResult term();

    AjaxResult getBoardCourseInfo(CpBoardBo condition);

    AjaxResult getBoardCourseCategory(CpBoardBo condition);

    AjaxResult getBoardGroupSourceType(CpBoardBo condition);

    AjaxResult getBoardResourceInfo(CpBoardBo condition);

    AjaxResult getBoardFreeUseInfo(CpBoardBo condition);

    AjaxResult getBoardSchoolFreeUseInfo(CpBoardBo condition);

    AjaxResult getBoardPayOpenInfo(CpBoardBo condition);

    AjaxResult getBoardPayCourseHot5(CpBoardBo condition);

    AjaxResult getBoardSchoolOpenInfo(CpBoardBo condition);

    AjaxResult getBoardEduSchoolPayCount(CpBoardBo condition);

    AjaxResult getBoardSchoolPayCount(CpBoardBo condition);

    AjaxResult getBoardSchoolOpenPage(CpBoardBo condition);

    AjaxResult getBoardVideoPlaySort(CpBoardBo condition);

    AjaxResult getBoardSchoolCategory(CpBoardBo condition);

    AjaxResult getBoardCourseCategoryUse(CpBoardBo condition);

    AjaxResult getBoardCourseCategoryDuration(CpBoardBo condition);

    AjaxResult getBoardSchoolCourseInfo(CpBoardBo condition);

    AjaxResult getBoardSchoolTeacherUseInfo(CpBoardBo condition);

    AjaxResult getBoardSingleCourseInfo(CpBoardBo condition);

    AjaxResult getBoardSingleUserInfo(CpBoardBo condition);

}
