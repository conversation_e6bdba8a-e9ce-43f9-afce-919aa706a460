package com.fh.cp.service.cp.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.light.core.utils.FuzzyQueryUtil;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

import com.light.core.enums.StatusEnum;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import javax.annotation.Resource;

import com.fh.cp.service.cp.entity.dto.CpTeacherVerifyDto;
import com.fh.cp.service.cp.entity.bo.CpTeacherVerifyConditionBo;
import com.fh.cp.service.cp.entity.bo.CpTeacherVerifyBo;
import com.fh.cp.service.cp.entity.vo.CpTeacherVerifyVo;
import com.fh.cp.service.cp.service.ICpTeacherVerifyService;
import com.fh.cp.service.cp.mapper.CpTeacherVerifyMapper;
import com.light.core.entity.AjaxResult;

/**
 * 机构教师审核表接口实现类
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2023-05-29 14:56:21
 */
@Service
public class CpTeacherVerifyServiceImpl extends ServiceImpl<CpTeacherVerifyMapper, CpTeacherVerifyDto>
    implements ICpTeacherVerifyService {

    @Resource
    private CpTeacherVerifyMapper cpTeacherVerifyMapper;

    @Override
    public List<CpTeacherVerifyVo> getCpTeacherVerifyListByCondition(CpTeacherVerifyConditionBo condition) {
        condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
        FuzzyQueryUtil.transferMeanBean(condition);
        return cpTeacherVerifyMapper.getCpTeacherVerifyListByCondition(condition);
    }

    @Override
    public AjaxResult addCpTeacherVerify(CpTeacherVerifyBo cpTeacherVerifyBo) {
        CpTeacherVerifyDto cpTeacherVerify = new CpTeacherVerifyDto();
        BeanUtils.copyProperties(cpTeacherVerifyBo, cpTeacherVerify);
        cpTeacherVerify.setIsDelete(StatusEnum.NOTDELETE.getCode());
        if (save(cpTeacherVerify)) {
            return AjaxResult.success("保存成功");
        } else {
            return AjaxResult.fail("保存失败");
        }
    }

    @Override
    public AjaxResult updateCpTeacherVerify(CpTeacherVerifyBo cpTeacherVerifyBo) {
        CpTeacherVerifyDto cpTeacherVerify = new CpTeacherVerifyDto();
        BeanUtils.copyProperties(cpTeacherVerifyBo, cpTeacherVerify);
        if (updateById(cpTeacherVerify)) {
            return AjaxResult.success("保存成功");
        } else {
            return AjaxResult.fail("保存失败");
        }
    }

    @Override
    public CpTeacherVerifyVo getCpTeacherVerifyByCondition(CpTeacherVerifyConditionBo condition) {
        condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
        return cpTeacherVerifyMapper.getCpTeacherVerifyByCondition(condition);
    }

    @Override
    public AjaxResult updateVerifyType(CpTeacherVerifyBo cpTeacherVerifyBo) {
        CpTeacherVerifyDto entity = getOne(new LambdaQueryWrapper<CpTeacherVerifyDto>()
            .eq(CpTeacherVerifyDto::getTeacherId, cpTeacherVerifyBo.getTeacherId())
            .eq(CpTeacherVerifyDto::getIsDelete, StatusEnum.NOTDELETE.getCode()).orderByDesc(CpTeacherVerifyDto::getId)
            .last("limit 1"));
        if (entity == null) {
            entity = new CpTeacherVerifyDto();
            BeanUtils.copyProperties(cpTeacherVerifyBo, entity);
            entity.setCreateBy(cpTeacherVerifyBo.getUserOid());
            entity.setCreateTime(new Date());
            if (save(entity)) {
                return AjaxResult.success("修改机构教师审核状态成功");
            }
        } else {
            entity.setTeacherVerifyType(cpTeacherVerifyBo.getTeacherVerifyType());
            entity.setUpdateBy(cpTeacherVerifyBo.getUserOid());
            entity.setUpdateTime(new Date());
            if (updateById(entity)) {
                return AjaxResult.success("修改机构教师审核状态成功");
            }
        }
        return AjaxResult.fail("修改机构教师审核状态失败");
    }

}