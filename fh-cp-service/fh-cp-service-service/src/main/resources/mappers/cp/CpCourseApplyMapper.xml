<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fh.cp.service.cp.mapper.CpCourseApplyMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.fh.cp.service.cp.entity.dto.CpCourseApplyDto" id="BaseResultMap">
        <result property="courseApplyId" column="course_apply_id"/>
        <result property="organizationId" column="organization_id"/>
        <result property="sourceType" column="source_type"/>
        <result property="uuid" column="uuid"/>
        <result property="connectType" column="connect_type"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="isDelete" column="is_delete"/>
    </resultMap>

	<sql id="common_where">
		<where>
			<if test="courseApplyId != null ">and course_apply_id = #{courseApplyId}</if>
			<if test="organizationId != null ">and organization_id = #{organizationId}</if>
			<if test="sourceType != null ">and source_type = #{sourceType}</if>
			<if test="uuid != null and uuid != '' ">and uuid like concat('%', #{uuid}, '%')</if>
			<if test="connectType != null ">and connect_type = #{connectType}</if>
			<if test="isDelete != null ">and is_delete = #{isDelete}</if>
			<if test="organizationIds != null and organizationIds.size() >0">
				and organization_id in
				<foreach collection="organizationIds" item="organizationId" open="(" close=")" separator=",">
					#{organizationId}
				</foreach>
			</if>
			<if test="courseApplyIds != null and courseApplyIds.size() >0">
				and course_apply_id in
				<foreach collection="courseApplyIds" item="courseApplyId" open="(" close=")" separator=",">
					#{courseApplyId}
				</foreach>
			</if>
		</where>
	</sql>

	<sql id="table_where">
		<where>
			<if test="courseApplyId != null ">and course_apply_id = #{courseApplyId}</if>
			<if test="organizationId != null ">and organization_id = #{organizationId}</if>
			<if test="sourceType != null ">and source_type = #{sourceType}</if>
			<if test="uuid != null and uuid != '' ">and uuid like concat('%', #{uuid}, '%')</if>
			<if test="connectType != null ">and connect_type = #{connectType}</if>
			<if test="isDelete != null ">and is_delete = #{isDelete}</if>
		</where>
	</sql>

	<sql id="common_select">
		select
	 		t.course_apply_id
	 		,t.organization_id
	 		,t.source_type
	 		,t.uuid
	 		,t.connect_type
	 		,t.create_time
	 		,t.create_by
	 		,t.update_time
	 		,t.update_by
	 		,t.is_delete
		from (
			select a.* from cp_course_apply a
			<include refid="table_where"></include>
		 ) t

	</sql>

	<select id="getCpCourseApplyListByCondition" resultType="com.fh.cp.service.cp.entity.vo.CpCourseApplyVo">
		<include refid="common_select"></include>
		<include refid="common_where"></include>
		order by create_time desc, course_apply_id desc
	</select>

	<select id="getCpCourseApplyByCondition" resultType="com.fh.cp.service.cp.entity.vo.CpCourseApplyVo">
		<include refid="common_select"></include>
		<include refid="common_where"></include>
		limit 1
	</select>
</mapper>